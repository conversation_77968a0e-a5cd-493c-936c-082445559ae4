"""
Reddit monitoring functionality for Reddit Discord Bot
"""
import praw
import time
import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from config import Config
from query_parser import QueryParser

logger = logging.getLogger(__name__)

class RedditMonitor:
    """Handles Reddit API integration and post monitoring"""
    
    def __init__(self, query_parser: QueryParser):
        self.query_parser = query_parser
        self.reddit = None
        self.seen_posts: Set[str] = set()
        self.last_check_time = datetime.utcnow()
        self._initialize_reddit()
    
    def _initialize_reddit(self) -> None:
        """Initialize Reddit API connection"""
        try:
            self.reddit = praw.Reddit(
                client_id=Config.REDDIT_CLIENT_ID,
                client_secret=Config.REDDIT_CLIENT_SECRET,
                user_agent=Config.REDDIT_USER_AGENT
            )
            
            # Test the connection
            self.reddit.user.me()
            logger.info("Successfully connected to Reddit API")
            
        except Exception as e:
            logger.error(f"Failed to initialize Reddit API: {e}")
            self.reddit = None
    
    def get_new_posts(self) -> List[Dict[str, Any]]:
        """
        Get new posts from monitored subreddits
        
        Returns:
            List of post dictionaries with relevant information
        """
        if not self.reddit:
            logger.error("Reddit API not initialized")
            return []
        
        new_posts = []
        subreddits_string = Config.get_subreddits_string()
        
        try:
            subreddit = self.reddit.subreddit(subreddits_string)
            
            # Get new posts (sorted by new)
            posts = subreddit.new(limit=Config.MAX_POSTS_PER_CHECK)
            
            for post in posts:
                # Skip if we've already seen this post
                if post.id in self.seen_posts:
                    continue
                
                # Skip if post is older than our last check (with some buffer)
                post_time = datetime.utcfromtimestamp(post.created_utc)
                if post_time < self.last_check_time - timedelta(minutes=5):
                    continue
                
                # Add to seen posts
                self.seen_posts.add(post.id)
                
                # Check if post matches any queries
                is_match, matched_query = self.query_parser.check_post_match(
                    post.title, 
                    post.selftext if hasattr(post, 'selftext') else None
                )
                
                if is_match:
                    post_data = self._extract_post_data(post, matched_query)
                    new_posts.append(post_data)
                    logger.info(f"Found matching post: {post.title[:50]}... (matched: {matched_query})")
            
            # Update last check time
            self.last_check_time = datetime.utcnow()
            
            # Clean up old seen posts (keep only last 1000)
            if len(self.seen_posts) > 1000:
                # Convert to list, sort, and keep newest 800
                seen_list = list(self.seen_posts)
                self.seen_posts = set(seen_list[-800:])
            
            logger.debug(f"Checked {Config.MAX_POSTS_PER_CHECK} posts, found {len(new_posts)} matches")
            
        except Exception as e:
            logger.error(f"Error fetching posts from Reddit: {e}")
        
        return new_posts
    
    def _extract_post_data(self, post, matched_query: str) -> Dict[str, Any]:
        """Extract relevant data from a Reddit post"""
        # Get post content with length limit
        content = ""
        if hasattr(post, 'selftext') and post.selftext:
            content = post.selftext[:500]  # Limit content length
            if len(post.selftext) > 500:
                content += "..."
        
        return {
            'id': post.id,
            'title': post.title,
            'url': f"https://reddit.com{post.permalink}",
            'subreddit': post.subreddit.display_name,
            'author': str(post.author) if post.author else '[deleted]',
            'content': content,
            'score': post.score,
            'created_utc': post.created_utc,
            'matched_query': matched_query,
            'num_comments': post.num_comments
        }
    
    def test_connection(self) -> bool:
        """Test Reddit API connection"""
        try:
            if not self.reddit:
                return False
            
            # Try to access a simple endpoint
            self.reddit.user.me()
            return True
            
        except Exception as e:
            logger.error(f"Reddit connection test failed: {e}")
            return False
    
    def get_subreddit_info(self) -> Dict[str, Any]:
        """Get information about monitored subreddits"""
        if not self.reddit:
            return {}
        
        info = {}
        for subreddit_name in Config.SUBREDDITS:
            try:
                subreddit = self.reddit.subreddit(subreddit_name)
                info[subreddit_name] = {
                    'subscribers': subreddit.subscribers,
                    'active_users': subreddit.active_user_count,
                    'description': subreddit.public_description[:100] + "..." if len(subreddit.public_description) > 100 else subreddit.public_description
                }
            except Exception as e:
                logger.warning(f"Could not get info for subreddit {subreddit_name}: {e}")
                info[subreddit_name] = {'error': str(e)}
        
        return info
