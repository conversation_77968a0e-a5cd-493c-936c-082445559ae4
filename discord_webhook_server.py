#!/usr/bin/env python3
"""
Simple Discord Webhook Test Server
Runs on localhost:5000 to receive and display Discord webhook payloads
"""
from flask import Flask, request, jsonify
import json
from datetime import datetime

app = Flask(__name__)

@app.route('/', methods=['POST'])
def receive_webhook():
    """Receive and display Discord webhook payload"""
    try:
        # Get the JSON payload
        payload = request.get_json()
        
        if not payload:
            return jsonify({"error": "No JSON payload received"}), 400
        
        # Log the received webhook
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        print(f"\n🎯 Webhook received at {timestamp}")
        print("=" * 50)
        
        # Display basic webhook info
        if 'username' in payload:
            print(f"👤 Username: {payload['username']}")
        
        if 'avatar_url' in payload:
            print(f"🖼️  Avatar URL: {payload['avatar_url']}")
        
        # Display embeds
        if 'embeds' in payload and payload['embeds']:
            print(f"\n📋 Embeds ({len(payload['embeds'])} found):")
            
            for i, embed in enumerate(payload['embeds'], 1):
                print(f"\n  Embed {i}:")
                
                if 'title' in embed:
                    print(f"    📝 Title: {embed['title']}")
                
                if 'url' in embed:
                    print(f"    🔗 URL: {embed['url']}")
                
                if 'color' in embed:
                    print(f"    🎨 Color: #{embed['color']:06x}")
                
                if 'author' in embed:
                    author = embed['author']
                    if 'name' in author:
                        print(f"    👤 Author: {author['name']}")
                    if 'url' in author:
                        print(f"    🔗 Author URL: {author['url']}")
                
                if 'footer' in embed:
                    footer = embed['footer']
                    if 'text' in footer:
                        print(f"    📄 Footer: {footer['text']}")
                
                if 'timestamp' in embed:
                    print(f"    🕒 Timestamp: {embed['timestamp']}")
                
                if 'fields' in embed and embed['fields']:
                    print(f"    📊 Fields ({len(embed['fields'])} found):")
                    for field in embed['fields']:
                        name = field.get('name', 'No name')
                        value = field.get('value', 'No value')
                        inline = field.get('inline', False)
                        print(f"      • {name}: {value} {'(inline)' if inline else ''}")
        
        # Display raw JSON for debugging
        print(f"\n🔍 Raw JSON payload:")
        print(json.dumps(payload, indent=2))
        print("=" * 50)
        
        # Return success response
        return jsonify({
            "status": "success",
            "message": "Webhook received successfully",
            "timestamp": timestamp
        }), 200
        
    except Exception as e:
        error_msg = f"Error processing webhook: {e}"
        print(f"\n❌ {error_msg}")
        return jsonify({"error": error_msg}), 500

@app.route('/', methods=['GET'])
def health_check():
    """Simple health check endpoint"""
    return jsonify({
        "status": "running",
        "message": "Discord Webhook Test Server is running",
        "timestamp": datetime.now().isoformat(),
        "endpoints": {
            "POST /": "Receive Discord webhook",
            "GET /": "Health check"
        }
    })

@app.route('/test', methods=['GET'])
def test_endpoint():
    """Test endpoint to verify server is working"""
    return jsonify({
        "status": "ok",
        "message": "Test endpoint working",
        "server": "Discord Webhook Test Server",
        "timestamp": datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting Discord Webhook Test Server")
    print("=" * 40)
    print("📡 Server will run on: http://localhost:5000")
    print("📝 Endpoints:")
    print("   GET  / - Health check")
    print("   POST / - Receive Discord webhooks")
    print("   GET  /test - Test endpoint")
    print("\n💡 Usage:")
    print("   1. Start this server")
    print("   2. Run reddit_to_discord.py to send a Reddit post")
    print("   3. Check the console output to see the received webhook")
    print("\n⏹️  Press Ctrl+C to stop the server")
    print("=" * 40)
    
    try:
        app.run(host='localhost', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n\n⏹️  Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
