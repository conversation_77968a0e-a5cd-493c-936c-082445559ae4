"""
Query parsing functionality for Reddit Discord Bot
"""
import re
import logging
from typing import List, Tuple, Optional
from config import Config

logger = logging.getLogger(__name__)

class QueryParser:
    """Handles parsing and matching of search queries"""
    
    def __init__(self, queries_file: str = None):
        self.queries_file = queries_file or Config.QUERIES_FILE
        self.queries = []
        self.load_queries()
    
    def load_queries(self) -> None:
        """Load queries from the queries file"""
        try:
            with open(self.queries_file, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                if content:
                    # Split by lines and filter out empty lines
                    raw_queries = [line.strip() for line in content.split('\n') if line.strip()]
                    self.queries = []
                    
                    for raw_query in raw_queries:
                        parsed_queries = self._parse_query_line(raw_query)
                        self.queries.extend(parsed_queries)
                    
                    logger.info(f"Loaded {len(self.queries)} search queries from {self.queries_file}")
                else:
                    logger.warning(f"Queries file {self.queries_file} is empty")
                    
        except FileNotFoundError:
            logger.error(f"Queries file {self.queries_file} not found")
            self.queries = []
        except Exception as e:
            logger.error(f"Error loading queries from {self.queries_file}: {e}")
            self.queries = []
    
    def _parse_query_line(self, line: str) -> List[str]:
        """Parse a single line that may contain OR operators"""
        # Remove quotes and split by OR (case insensitive)
        line = line.strip('"\'')
        
        # Split by OR (with optional spaces around it)
        queries = re.split(r'\s+OR\s+', line, flags=re.IGNORECASE)
        
        # Clean up each query
        cleaned_queries = []
        for query in queries:
            query = query.strip().strip('"\'')
            if query:
                cleaned_queries.append(query.lower())  # Convert to lowercase for matching
        
        return cleaned_queries
    
    def check_match(self, text: str) -> Tuple[bool, Optional[str]]:
        """
        Check if text matches any of the loaded queries
        
        Args:
            text: Text to check (will be converted to lowercase)
            
        Returns:
            Tuple of (is_match, matched_query)
        """
        if not text or not self.queries:
            return False, None
        
        text_lower = text.lower()
        
        for query in self.queries:
            if query in text_lower:
                logger.debug(f"Found match: '{query}' in text")
                return True, query
        
        return False, None
    
    def check_post_match(self, title: str, content: str = None) -> Tuple[bool, Optional[str]]:
        """
        Check if a Reddit post matches any queries
        
        Args:
            title: Post title
            content: Post content/selftext (optional)
            
        Returns:
            Tuple of (is_match, matched_query)
        """
        # Check title first
        is_match, matched_query = self.check_match(title)
        if is_match:
            return True, matched_query
        
        # Check content if available
        if content:
            is_match, matched_query = self.check_match(content)
            if is_match:
                return True, matched_query
        
        return False, None
    
    def reload_queries(self) -> None:
        """Reload queries from file"""
        logger.info("Reloading queries from file")
        self.load_queries()
    
    def get_query_count(self) -> int:
        """Get the number of loaded queries"""
        return len(self.queries)
    
    def get_queries(self) -> List[str]:
        """Get a copy of all loaded queries"""
        return self.queries.copy()
