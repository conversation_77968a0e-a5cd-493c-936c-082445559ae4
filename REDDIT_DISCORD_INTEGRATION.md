# Reddit to Discord Integration

This integration allows you to fetch Reddit posts and send them to Discord via a localhost:5000 endpoint.

## Files Created

1. **`reddit_to_discord.py`** - Main script that fetches Reddit posts and sends them to Discord
2. **`discord_webhook_server.py`** - Test server that runs on localhost:5000 to receive Discord webhooks
3. **`run_reddit_discord_test.py`** - Helper script to run the integration test
4. **`simple_reddit_test.py`** - Updated with Discord integration functionality

## Quick Start

### Option 1: Automatic Test Runner

```bash
python run_reddit_discord_test.py
```

Choose option 3 for a full integration test.

### Option 2: Manual Setup

1. **Start the webhook server** (in one terminal):
   ```bash
   python discord_webhook_server.py
   ```

2. **Run the Reddit client** (in another terminal):
   ```bash
   python reddit_to_discord.py
   ```

## How It Works

1. **Reddit Fetching**: Uses Reddit's public API (no authentication required) to fetch posts
2. **Discord Formatting**: Converts Reddit post data into Discord embed format
3. **Local Webhook**: Sends the formatted data to localhost:5000 as a Discord webhook
4. **Display**: The test server displays the received webhook data

## Features

- ✅ No Reddit API credentials required (uses public API)
- ✅ Fetches posts from any public subreddit
- ✅ Creates rich Discord embeds with:
  - Post title and URL
  - Author information
  - Upvotes and comment count
  - Post timestamp
  - Content preview (if text post)
- ✅ Test server to verify webhook reception
- ✅ Error handling and user-friendly output

## Example Output

When you run the integration, you'll see:

```
🤖 Reddit to Discord Bot
==============================
Enter subreddit name (default: programming): 

📡 Fetching post from r/programming...
   ✅ Successfully fetched post: How to build a REST API with Python...

📋 Post Details:
   Title: How to build a REST API with Python and Flask
   Author: u/developer123
   Subreddit: r/programming
   Score: 245 upvotes
   Comments: 42
   URL: https://reddit.com/r/programming/comments/...

📤 Sending to Discord via http://localhost:5000...
   ✅ Successfully sent to Discord!

🎉 Successfully sent Reddit post to Discord!
```

## Customization

### Change the Target URL

Edit the `webhook_url` parameter in `reddit_to_discord.py`:

```python
# Instead of localhost:5000, use your Discord webhook URL
success = send_to_discord(embed, "https://discord.com/api/webhooks/YOUR_WEBHOOK_URL")
```

### Change the Subreddit

The script will prompt you for a subreddit, or you can modify the default in the code:

```python
# Change default subreddit
subreddit = input("Enter subreddit name (default: technology): ").strip()
if not subreddit:
    subreddit = "technology"  # Changed from "programming"
```

### Modify the Discord Embed

Edit the `create_discord_embed()` function in `reddit_to_discord.py` to customize:
- Colors
- Fields
- Footer text
- Author display

## Integration with Existing Bot

To integrate this with your existing Reddit bot:

1. **Import the functions**:
   ```python
   from reddit_to_discord import create_discord_embed, send_to_discord
   ```

2. **Use in your monitoring loop**:
   ```python
   # When you find a matching post
   embed = create_discord_embed(post_data)
   success = send_to_discord(embed, "http://localhost:5000")
   ```

3. **Replace localhost:5000** with your actual Discord webhook URL

## Troubleshooting

### Server Not Starting
- Make sure port 5000 is not in use
- Install Flask: `pip install flask`

### Reddit API Errors
- Check your internet connection
- Try a different subreddit (some may be private)
- Reddit may be rate-limiting; wait a few minutes

### Discord Webhook Errors
- Verify the webhook URL is correct
- Check that the Discord server/channel exists
- Ensure the webhook has proper permissions

## Dependencies

Make sure you have the required packages:

```bash
pip install -r requirements.txt
```

The integration adds Flask to the requirements for the test server.
