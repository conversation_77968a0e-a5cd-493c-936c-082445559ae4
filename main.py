#!/usr/bin/env python3
"""
Reddit Discord Bot - Main Application
Monitors Reddit for new posts and sends Discord notifications when posts match specified criteria.
"""
import time
import signal
import sys
import logging
from datetime import datetime, timedelta
from typing import Optional

from config import Config
from logger_setup import setup_logging, log_startup_info, BotStats
from query_parser import QueryParser
from reddit_monitor import RedditMonitor
from discord_notifier import DiscordNotifier
from error_handler import (
    retry_on_failure, handle_reddit_errors, handle_discord_errors,
    safe_execute, handle_rate_limit, ErrorRecovery, ConfigurationError,
    APIError, RateLimitError
)

class RedditDiscordBot:
    """Main bot class that orchestrates all components"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.stats = BotStats()
        self.running = False
        self.query_parser: Optional[QueryParser] = None
        self.reddit_monitor: Optional[RedditMonitor] = None
        self.discord_notifier: Optional[DiscordNotifier] = None
        self.reddit_recovery = ErrorRecovery(failure_threshold=3, recovery_timeout=300)
        self.discord_recovery = ErrorRecovery(failure_threshold=5, recovery_timeout=180)
        self.last_stats_log = datetime.now()
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
    
    def initialize(self) -> bool:
        """Initialize all bot components"""
        try:
            log_startup_info(self.logger)
            
            # Validate configuration
            config_errors = Config.validate_config()
            if config_errors:
                for error in config_errors:
                    self.logger.error(f"Configuration error: {error}")
                raise ConfigurationError(f"Configuration validation failed: {', '.join(config_errors)}")
            
            # Initialize components
            self.logger.info("Initializing components...")
            
            # Query parser
            self.query_parser = QueryParser()
            if self.query_parser.get_query_count() == 0:
                self.logger.warning("No queries loaded - bot will not find any matches")
            else:
                self.logger.info(f"Loaded {self.query_parser.get_query_count()} search queries")
            
            # Reddit monitor
            self.reddit_monitor = RedditMonitor(self.query_parser)
            if not self.reddit_monitor.test_connection():
                raise ConfigurationError("Failed to connect to Reddit API")
            
            # Discord notifier
            self.discord_notifier = DiscordNotifier()
            if not self.discord_notifier.test_connection():
                raise ConfigurationError("Failed to connect to Discord")
            
            self.logger.info("All components initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize bot: {e}")
            return False
    
    @retry_on_failure(max_retries=2, delay=5.0)
    @handle_reddit_errors
    def _check_reddit_posts(self) -> list:
        """Check for new Reddit posts with error handling"""
        if not self.reddit_recovery.can_execute():
            self.logger.warning("Reddit circuit breaker is open, skipping check")
            return []
        
        try:
            posts = self.reddit_monitor.get_new_posts()
            self.reddit_recovery.record_success()
            return posts
        except (APIError, RateLimitError) as e:
            self.reddit_recovery.record_failure()
            if isinstance(e, RateLimitError):
                handle_rate_limit(e, "Reddit")
            raise
    
    @retry_on_failure(max_retries=3, delay=2.0)
    @handle_discord_errors
    def _send_discord_notification(self, post_data: dict) -> bool:
        """Send Discord notification with error handling"""
        if not self.discord_recovery.can_execute():
            self.logger.warning("Discord circuit breaker is open, skipping notification")
            return False
        
        try:
            success = self.discord_notifier.send_post_notification(post_data)
            if success:
                self.discord_recovery.record_success()
            else:
                self.discord_recovery.record_failure()
            return success
        except (APIError, RateLimitError) as e:
            self.discord_recovery.record_failure()
            if isinstance(e, RateLimitError):
                handle_rate_limit(e, "Discord")
            raise
    
    def _log_periodic_stats(self) -> None:
        """Log periodic statistics"""
        now = datetime.now()
        if now - self.last_stats_log >= timedelta(hours=1):  # Log stats every hour
            self.stats.log_periodic_stats(self.logger)
            self.last_stats_log = now
    
    def run_monitoring_cycle(self) -> None:
        """Run a single monitoring cycle"""
        try:
            # Check for new posts
            success, posts = safe_execute(self._check_reddit_posts)
            
            if not success:
                self.stats.increment_errors()
                return
            
            if posts:
                self.logger.info(f"Found {len(posts)} matching posts")
                
                for post_data in posts:
                    self.stats.increment_matches_found()
                    
                    # Send Discord notification
                    success, _ = safe_execute(self._send_discord_notification, post_data)
                    
                    if success:
                        self.stats.increment_notifications_sent()
                        self.logger.info(f"Sent notification for: {post_data['title'][:50]}...")
                    else:
                        self.stats.increment_errors()
                        self.logger.error(f"Failed to send notification for: {post_data['title'][:50]}...")
                    
                    # Small delay between notifications to avoid rate limits
                    time.sleep(1)
            
            self.stats.increment_posts_checked(Config.MAX_POSTS_PER_CHECK)
            
        except Exception as e:
            self.logger.error(f"Error in monitoring cycle: {e}")
            self.stats.increment_errors()
    
    def run(self) -> None:
        """Main bot loop"""
        if not self.initialize():
            self.logger.error("Failed to initialize bot, exiting")
            sys.exit(1)
        
        self.running = True
        self.logger.info(f"Starting monitoring loop (check interval: {Config.CHECK_INTERVAL}s)")
        
        # Send startup notification
        startup_msg = f"🚀 Reddit Monitor started!\nMonitoring: {', '.join(Config.SUBREDDITS)}\nQueries: {self.query_parser.get_query_count()}"
        safe_execute(self.discord_notifier.send_status_message, startup_msg)
        
        try:
            while self.running:
                cycle_start = time.time()
                
                # Run monitoring cycle
                self.run_monitoring_cycle()
                
                # Log periodic statistics
                self._log_periodic_stats()
                
                # Calculate sleep time
                cycle_duration = time.time() - cycle_start
                sleep_time = max(0, Config.CHECK_INTERVAL - cycle_duration)
                
                if sleep_time > 0:
                    self.logger.debug(f"Cycle completed in {cycle_duration:.2f}s, sleeping for {sleep_time:.2f}s")
                    time.sleep(sleep_time)
                else:
                    self.logger.warning(f"Cycle took {cycle_duration:.2f}s, longer than interval {Config.CHECK_INTERVAL}s")
        
        except KeyboardInterrupt:
            self.logger.info("Received keyboard interrupt")
        except Exception as e:
            self.logger.error(f"Unexpected error in main loop: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self) -> None:
        """Graceful shutdown"""
        self.logger.info("Shutting down bot...")
        
        # Send shutdown notification
        if self.discord_notifier:
            shutdown_msg = f"🛑 Reddit Monitor shutting down\nFinal stats:\n" + \
                          "\n".join([f"{k}: {v}" for k, v in self.stats.get_stats_dict().items()])
            safe_execute(self.discord_notifier.send_status_message, shutdown_msg)
            self.discord_notifier.close()
        
        # Log final statistics
        self.stats.log_periodic_stats(self.logger)
        self.logger.info("Bot shutdown complete")

def main():
    """Main entry point"""
    bot = RedditDiscordBot()
    bot.run()

if __name__ == "__main__":
    main()
