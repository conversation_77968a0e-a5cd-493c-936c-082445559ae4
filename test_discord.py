#!/usr/bin/env python3
"""
Test script to check if Discord integration is working
Sends a random test message to Discord and fetches/sends Reddit posts
"""

import random
import sys
from datetime import datetime
from discord_notifier import DiscordNotifier
from reddit_monitor import RedditMonitor
from query_parser import QueryParser
from config import Config

def test_reddit_post_to_discord():
    """Test fetching Reddit posts and sending them to Discord"""
    print("\n🔍 Testing Reddit Post Fetching & Discord Integration")
    print("=" * 60)

    # Initialize query parser
    print("📝 Loading search queries...")
    try:
        query_parser = QueryParser()
        query_count = query_parser.get_query_count()

        if query_count == 0:
            print("❌ No queries loaded from queries.txt")
            return False

        print(f"✅ Loaded {query_count} search queries")

        # Show sample queries
        queries = query_parser.get_queries()
        print("   📋 Sample queries:")
        for i, query in enumerate(queries[:3]):
            print(f"      {i+1}. {query}")
        if len(queries) > 3:
            print(f"      ... and {len(queries) - 3} more")

    except Exception as e:
        print(f"❌ Failed to load queries: {e}")
        return False

    # Initialize Reddit monitor
    print("\n📡 Initializing Reddit monitor...")
    try:
        reddit_monitor = RedditMonitor(query_parser)

        if not reddit_monitor.test_connection():
            print("❌ Failed to connect to Reddit API")
            return False

        print("✅ Successfully connected to Reddit API")

    except Exception as e:
        print(f"❌ Failed to initialize Reddit monitor: {e}")
        return False

    # Initialize Discord notifier
    print("\n💬 Initializing Discord notifier...")
    try:
        discord_notifier = DiscordNotifier()
        print("✅ Discord notifier initialized")
    except Exception as e:
        print(f"❌ Failed to initialize Discord notifier: {e}")
        return False

    # Fetch posts from Reddit
    print(f"\n🔎 Searching for posts in subreddits: {', '.join(Config.SUBREDDITS)}")
    try:
        posts = reddit_monitor.get_new_posts()

        if not posts:
            print("⚠️  No matching posts found with current queries")
            print("� Let's try fetching any recent posts to test Discord formatting...")

            # Fetch any recent posts for testing (bypass query matching)
            posts = fetch_recent_posts_for_testing(reddit_monitor)

            if not posts:
                print("❌ No recent posts found at all")
                return True  # Not an error, just no posts available

        print(f"🎯 Found {len(posts)} matching posts!")

        # Send each post to Discord
        success_count = 0
        for i, post in enumerate(posts[:3], 1):  # Limit to first 3 posts
            print(f"\n📤 Sending post {i}/{min(len(posts), 3)} to Discord...")
            print(f"   📝 Title: {post['title'][:60]}...")
            print(f"   🏷️  Matched query: {post['matched_query']}")
            print(f"   📊 Score: {post['score']} | Comments: {post['num_comments']}")

            try:
                success = discord_notifier.send_post_notification(post)
                if success:
                    print(f"   ✅ Successfully sent to Discord!")
                    success_count += 1
                else:
                    print(f"   ❌ Failed to send to Discord")
            except Exception as e:
                print(f"   ❌ Error sending to Discord: {e}")

        print(f"\n🎉 Successfully sent {success_count}/{min(len(posts), 3)} posts to Discord!")
        return success_count > 0

    except Exception as e:
        print(f"❌ Error fetching posts: {e}")
        return False

def main():
    """Test Discord connection and Reddit post fetching"""
    print("🔧 Testing Discord & Reddit Integration")
    print("=" * 50)
    
    # Check if Discord is configured
    if not Config.DISCORD_WEBHOOK_URL and not (Config.DISCORD_BOT_TOKEN and Config.DISCORD_CHANNEL_ID):
        print("❌ Discord not configured!")
        print("Please set either DISCORD_WEBHOOK_URL or both DISCORD_BOT_TOKEN and DISCORD_CHANNEL_ID in your .env file")
        return False

    # Show current configuration
    if Config.DISCORD_WEBHOOK_URL:
        print(f"📡 Using Discord Webhook")
        print(f"   URL: {Config.DISCORD_WEBHOOK_URL[:50]}...")
    else:
        print(f"🤖 Using Discord Bot")
        print(f"   Channel ID: {Config.DISCORD_CHANNEL_ID}")

    print()

    # Create Discord notifier
    try:
        notifier = DiscordNotifier()
        print("✅ Discord notifier created successfully")
    except Exception as e:
        print(f"❌ Failed to create Discord notifier: {e}")
        return False

    # Generate random test messages
    random_messages = [
        "🎲 Random test message - Discord is working!",
        "🚀 Beep boop! This is a test from your Reddit bot",
        "🎯 Testing... testing... 1, 2, 3!",
        "🌟 Hello Discord! Your bot is alive and kicking",
        "🔥 Random message to verify Discord connectivity",
        "⚡ Zap! Your Discord integration is functional",
        "🎪 Circus test! Discord webhook is responding",
        "🎨 Colorful test message from your Reddit monitor",
        "🎵 La la la... just testing the Discord connection",
        "🎮 Game over? Nope! Discord is working perfectly"
    ]

    # Pick a random message
    random_message = random.choice(random_messages)
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    test_message = f"{random_message}\n\n⏰ Sent at: {timestamp}"

    print(f"📤 Sending random test message...")
    print(f"   Message: {random_message}")
    print()

    # Test basic connection first
    print("🔍 Testing basic connection...")
    if notifier.test_connection():
        print("✅ Basic connection test passed!")
    else:
        print("❌ Basic connection test failed!")
        return False

    print()

    # Send the random message
    print("📨 Sending random message...")
    success = notifier.send_status_message(test_message)

    if success:
        print("✅ Random message sent successfully!")
        print("🎉 Discord basic test completed!")
    else:
        print("❌ Failed to send random message!")
        print("💡 Check your Discord webhook URL or bot configuration")
        return False

    # Now test Reddit post fetching and sending
    reddit_success = test_reddit_post_to_discord()

    if success and reddit_success:
        print("\n🎊 All tests completed successfully!")
        print("✅ Discord is working")
        print("✅ Reddit integration is working")
        print("✅ Posts can be fetched and sent to Discord")
        return True
    elif success:
        print("\n⚠️  Mixed results:")
        print("✅ Discord basic functionality works")
        print("❌ Reddit post integration had issues")
        return True  # Discord works, which was the main question
    else:
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n👋 Test cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        sys.exit(1)
