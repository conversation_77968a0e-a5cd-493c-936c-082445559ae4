"""
Discord notification functionality for Reddit Discord Bot
"""
import requests
import json
import logging
from typing import Dict, Any, Optional
from datetime import datetime
from config import Config

logger = logging.getLogger(__name__)

class DiscordNotifier:
    """Handles Discord notifications via webhook or bot"""
    
    def __init__(self):
        self.webhook_url = Config.DISCORD_WEBHOOK_URL
        self.bot_token = Config.DISCORD_BOT_TOKEN
        self.channel_id = Config.DISCORD_CHANNEL_ID
        self.session = requests.Session()
        
        # Set up headers for bot API if using bot token
        if self.bot_token:
            self.session.headers.update({
                'Authorization': f'Bot {self.bot_token}',
                'Content-Type': 'application/json'
            })
    
    def send_post_notification(self, post_data: Dict[str, Any]) -> bool:
        """
        Send a Discord notification for a matching Reddit post
        
        Args:
            post_data: Dictionary containing post information
            
        Returns:
            True if notification was sent successfully, False otherwise
        """
        try:
            if self.webhook_url:
                return self._send_webhook_notification(post_data)
            elif self.bot_token and self.channel_id:
                return self._send_bot_notification(post_data)
            else:
                logger.error("No Discord configuration found (webhook URL or bot token + channel ID)")
                return False
                
        except Exception as e:
            logger.error(f"Error sending Discord notification: {e}")
            return False
    
    def _send_webhook_notification(self, post_data: Dict[str, Any]) -> bool:
        """Send notification via Discord webhook"""
        embed = self._create_embed(post_data)
        
        payload = {
            "username": "Reddit Monitor",
            "avatar_url": "https://www.redditstatic.com/desktop2x/img/favicon/android-icon-192x192.png",
            "embeds": [embed]
        }
        
        try:
            response = self.session.post(self.webhook_url, json=payload)
            response.raise_for_status()
            logger.info(f"Successfully sent webhook notification for post: {post_data['title'][:50]}...")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send webhook notification: {e}")
            return False
    
    def _send_bot_notification(self, post_data: Dict[str, Any]) -> bool:
        """Send notification via Discord bot API"""
        embed = self._create_embed(post_data)
        
        payload = {
            "embeds": [embed]
        }
        
        url = f"https://discord.com/api/v10/channels/{self.channel_id}/messages"
        
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            logger.info(f"Successfully sent bot notification for post: {post_data['title'][:50]}...")
            return True
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to send bot notification: {e}")
            return False
    
    def _create_embed(self, post_data: Dict[str, Any]) -> Dict[str, Any]:
        """Create a Discord embed for the Reddit post"""
        # Format timestamp
        timestamp = datetime.utcfromtimestamp(post_data['created_utc']).isoformat() + 'Z'
        
        # Determine embed color based on subreddit
        color_map = {
            'forhire': 0x00ff00,      # Green
            'slavelabour': 0x0099ff,  # Blue
            'jobs4bitcoins': 0xffa500, # Orange
        }
        color = color_map.get(post_data['subreddit'].lower(), 0x5865f2)  # Default Discord blue
        
        embed = {
            "title": post_data['title'][:256],  # Discord title limit
            "url": post_data['url'],
            "color": color,
            "timestamp": timestamp,
            "footer": {
                "text": f"r/{post_data['subreddit']} • Matched: {post_data['matched_query']}"
            },
            "author": {
                "name": f"u/{post_data['author']}",
                "url": f"https://reddit.com/u/{post_data['author']}"
            },
            "fields": [
                {
                    "name": "📊 Stats",
                    "value": f"👍 {post_data['score']} • 💬 {post_data['num_comments']}",
                    "inline": True
                },
                {
                    "name": "🔍 Matched Query",
                    "value": f"`{post_data['matched_query']}`",
                    "inline": True
                }
            ]
        }
        
        # Add content if available
        if post_data.get('content'):
            content = post_data['content']
            if len(content) > 1024:  # Discord field value limit
                content = content[:1021] + "..."
            
            embed["fields"].append({
                "name": "📝 Content",
                "value": content,
                "inline": False
            })
        
        return embed
    
    def send_status_message(self, message: str, is_error: bool = False) -> bool:
        """
        Send a status message to Discord
        
        Args:
            message: Status message to send
            is_error: Whether this is an error message
            
        Returns:
            True if message was sent successfully, False otherwise
        """
        embed = {
            "title": "🤖 Reddit Monitor Status",
            "description": message,
            "color": 0xff0000 if is_error else 0x00ff00,  # Red for error, green for success
            "timestamp": datetime.utcnow().isoformat() + 'Z'
        }
        
        try:
            if self.webhook_url:
                payload = {
                    "username": "Reddit Monitor",
                    "embeds": [embed]
                }
                response = self.session.post(self.webhook_url, json=payload)
                response.raise_for_status()
                
            elif self.bot_token and self.channel_id:
                payload = {"embeds": [embed]}
                url = f"https://discord.com/api/v10/channels/{self.channel_id}/messages"
                response = self.session.post(url, json=payload)
                response.raise_for_status()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send status message: {e}")
            return False
    
    def test_connection(self) -> bool:
        """Test Discord connection"""
        return self.send_status_message("✅ Discord connection test successful!")
    
    def close(self) -> None:
        """Close the requests session"""
        self.session.close()
